{"name": "@nestjs/core", "version": "11.1.5", "description": "Nest - modern, fast, powerful node.js web framework (@core)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://nestjs.com", "funding": {"type": "opencollective", "url": "https://opencollective.com/nest"}, "engines": {"node": ">= 20"}, "repository": {"type": "git", "url": "https://github.com/nestjs/nest.git", "directory": "packages/core"}, "publishConfig": {"access": "public"}, "scripts": {"postinstall": "opencollective || exit 0"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/nest", "donation": {"text": "Become a partner:"}}, "dependencies": {"@nuxt/opencollective": "0.4.1", "fast-safe-stringify": "2.1.1", "iterare": "1.2.1", "path-to-regexp": "8.2.0", "tslib": "2.8.1", "uid": "2.0.2"}, "devDependencies": {"@nestjs/common": "11.1.5"}, "peerDependencies": {"@nestjs/common": "^11.0.0", "@nestjs/microservices": "^11.0.0", "@nestjs/platform-express": "^11.0.0", "@nestjs/websockets": "^11.0.0", "reflect-metadata": "^0.1.12 || ^0.2.0", "rxjs": "^7.1.0"}, "peerDependenciesMeta": {"@nestjs/websockets": {"optional": true}, "@nestjs/microservices": {"optional": true}, "@nestjs/platform-express": {"optional": true}}}