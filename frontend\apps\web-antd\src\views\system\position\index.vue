<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';

import { Page } from '@vben/common-ui';
import { onMounted, ref } from 'vue';

import { useVbenModal, VbenButton, type VbenFormProps } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import PositionModal from './components/PositionModal.vue';
import OrganizationSidebar from '../organization/components/OrganizationSidebar.vue';
import {
  getPositionTree,
  deletePosition,
  // batchDeletePositions
} from './position.api';
import {
  columns,
  searchFormSchema,
  getTypeLabel,
  getStatusLabel,
  buildTree
} from './position.data';
import { showConform } from '#/utils/alert.js';
import { message } from 'ant-design-vue';

// 组织筛选状态
const selectedOrganizationId = ref<number | null>(null);
const selectedOrganizationName = ref('全部组织');

const [CreateModal, createModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: PositionModal,
  fullscreenButton: false,
  destroyOnClose: true,
  onClosed: () => {
    gridApi.query();
  }
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: searchFormSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  },
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const gridOptions: VxeGridProps = {
  columns,
  height: 'auto',
  treeConfig: {
    childrenField: 'children',
    expandAll: true,
    accordion: false,
  },
  proxyConfig: {
    ajax: {
      query: async (_: any, formValues: any) => {
        const queryParams = {
          ...formValues,
        };

        // 如果选择了组织，添加组织筛选条件
        if (selectedOrganizationId.value) {
          queryParams.departmentId = selectedOrganizationId.value;
        }

        const data = await getPositionTree(queryParams);

        // 构建树形结构
        // const treeData = buildTree(data || []);

        return {
          total: data?.length || 0,
          items: data,
        };
      },
    },
  },
  rowConfig: {
    isHover: true,
  },
  checkboxConfig: {
    labelField: 'name',
    reserve: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 新增事件
 */
const handleAdd = (parentId?: number) => {
  createModalApi.setState({ parentId });
  createModalApi.open();
};

/**
 * 编辑事件
 */
const handleEdit = (row: any) => {
  createModalApi.setState({ row });
  createModalApi.open();
};

/**
 * 添加子岗位事件
 */
const handleAddChild = (row: any) => {
  createModalApi.setState({ parentId: row.id });
  createModalApi.open();
};

/**
 * 删除事件
 */
const handleDelete = async (row: any) => {
  const isConfirmed = await showConform({
    title: '确认删除',
    content: `确定要删除岗位"${row.name}"吗？`,
  });

  if (isConfirmed) {
    try {
      await deletePosition(row.id);
      message.success('删除成功');
      gridApi.query();
    } catch (error) {
      console.error('删除失败:', error);
      message.error(error.message || '删除失败');
    }
  }
};

/**
 * 批量删除事件
 */
// const handleBatchDelete = async () => {
//   const selectedRows = gridApi.getCheckboxRecords();
//   if (selectedRows.length === 0) {
//     message.warning('请选择要删除的岗位');
//     return;
//   }

//   const isConfirmed = await showConform({
//     title: '确认删除',
//     content: `确定要删除选中的 ${selectedRows.length} 个岗位吗？`,
//   });

//   if (isConfirmed) {
//     try {
//       const ids = selectedRows.map(row => row.id);
//       await batchDeletePositions(ids);
//       message.success('删除成功');
//       gridApi.query();
//     } catch (error) {
//       console.error('批量删除失败:', error);
//       message.error(error.message || '批量删除失败');
//     }
//   }
// };

/**
 * 组织选择事件
 */
const handleOrganizationSelect = (organizationId: number | null, organizationName: string) => {
  selectedOrganizationId.value = organizationId;
  selectedOrganizationName.value = organizationName;

  // 重新查询数据
  gridApi.query();
};

/**
 * 更新事件
 */
const onUpdate = () => {
  gridApi.query();
};

// Fetch data on component mounted
onMounted(() => {
  // 初始化数据
});
</script>

<template>
  <Page auto-content-height>
    <div class="position-management">
      <!-- 左侧组织筛选侧边栏 -->
      <div class="sidebar-container">
        <OrganizationSidebar
          :selected-organization-id="selectedOrganizationId"
          @select="handleOrganizationSelect"
        />
      </div>

      <!-- 右侧岗位管理内容 -->
      <div class="content-container">

        <!--引用表格-->
        <Grid>
      <!--插槽:table标题-->
      <template #toolbar-tools>
        <VbenButton
          pre-icon="ant-design:plus-outlined"
          type="primary"
          @click="handleAdd()"
        >
          新增岗位
        </VbenButton>
      </template>

      <!-- 类别插槽 -->
      <template #type="{ row }">
        <span>{{ getTypeLabel(row.type) }}</span>
      </template>

      <!-- 状态插槽 -->
      <template #status="{ row }">
        <a-tag :color="row.status === 1 ? 'green' : 'red'">
          {{ getStatusLabel(row.status) }}
        </a-tag>
      </template>

      <!--操作栏-->
      <template #action="{ row }">
        <div class="actionBar">
          <a-button
            class="actionButton"
            type="link"
            @click="handleEdit(row)"
          >
            编辑
          </a-button>
          <a-button
            class="actionButton"
            type="link"
            @click="handleAddChild(row)"
          >
            添加子岗位
          </a-button>
          <a-button
            class="actionButton"
            type="link"
            @click="handleDelete(row)"
          >
            删除
          </a-button>
        </div>
      </template>
        </Grid>

        <CreateModal @success="onUpdate" />
      </div>
    </div>
  </Page>
</template>

<style scoped lang="less">
@import '#/styles/dark-antd.less';

.position-management {
  display: flex;
  height: 100%;
  gap: 0;

  .sidebar-container {
    width: 280px;
    flex-shrink: 0;
    height: 100%;
    background: #fff;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  }

  .content-container {
    flex: 1;
    margin-left: 16px;
    display: flex;
    flex-direction: column;
    min-width: 0; // 防止flex子项溢出
  }
}

.actionButton {
  padding: 6px;
  color: hsl(var(--primary));
}

.actionButton:hover {
  color: hsl(var(--primary));
}

.actionBar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

// 响应式设计
@media (max-width: 1200px) {
  .position-management {
    .sidebar-container {
      width: 240px;
    }
  }
}

@media (max-width: 992px) {
  .position-management {
    flex-direction: column;

    .sidebar-container {
      width: 100%;
      height: 300px;
      margin-bottom: 16px;
    }

    .content-container {
      margin-left: 0;
    }
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .position-management {
    .sidebar-container {
      background: #141414;
      box-shadow: 0 1px 2px 0 rgba(255, 255, 255, 0.03), 0 1px 6px -1px rgba(255, 255, 255, 0.02), 0 2px 4px 0 rgba(255, 255, 255, 0.02);
    }
  }
}
</style>
