/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { TaskConfiguration, TaskConfigurationGenerator } from '../../src';
import { NodePackageTaskOptions } from './options';
interface NodePackageInstallTaskOptions {
    packageManager?: string;
    packageName?: string;
    workingDirectory?: string;
    quiet?: boolean;
    hideOutput?: boolean;
    allowScripts?: boolean;
}
export declare class NodePackageInstallTask implements TaskConfigurationGenerator<NodePackageTaskOptions> {
    quiet: boolean;
    hideOutput: boolean;
    allowScripts: boolean;
    workingDirectory?: string;
    packageManager?: string;
    packageName?: string;
    constructor(workingDirectory?: string);
    constructor(options: NodePackageInstallTaskOptions);
    toConfiguration(): TaskConfiguration<NodePackageTaskOptions>;
}
export {};
