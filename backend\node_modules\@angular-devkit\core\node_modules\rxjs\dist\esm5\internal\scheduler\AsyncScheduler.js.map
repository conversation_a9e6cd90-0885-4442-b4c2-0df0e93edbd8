{"version": 3, "file": "AsyncScheduler.js", "sourceRoot": "", "sources": ["../../../../src/internal/scheduler/AsyncScheduler.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAKzC;IAAoC,kCAAS;IAkB3C,wBAAY,eAA8B,EAAE,GAAiC;QAAjC,oBAAA,EAAA,MAAoB,SAAS,CAAC,GAAG;QAA7E,YACE,kBAAM,eAAe,EAAE,GAAG,CAAC,SAC5B;QAnBM,aAAO,GAA4B,EAAE,CAAC;QAOtC,aAAO,GAAY,KAAK,CAAC;;IAYhC,CAAC;IAEM,8BAAK,GAAZ,UAAa,MAAwB;QAC3B,IAAA,OAAO,GAAK,IAAI,QAAT,CAAU;QAEzB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrB,OAAO;SACR;QAED,IAAI,KAAU,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,GAAG;YACD,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBACxD,MAAM;aACP;SACF,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,EAAG,CAAC,EAAE;QAEtC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QAErB,IAAI,KAAK,EAAE;YACT,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,EAAG,CAAC,EAAE;gBAClC,MAAM,CAAC,WAAW,EAAE,CAAC;aACtB;YACD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IACH,qBAAC;AAAD,CAAC,AAhDD,CAAoC,SAAS,GAgD5C"}