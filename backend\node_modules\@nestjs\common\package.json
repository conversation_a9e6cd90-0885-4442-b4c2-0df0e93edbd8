{"name": "@nestjs/common", "version": "11.1.5", "description": "Nest - modern, fast, powerful node.js web framework (@common)", "author": "<PERSON><PERSON><PERSON>", "homepage": "https://nestjs.com", "funding": {"type": "opencollective", "url": "https://opencollective.com/nest"}, "repository": {"type": "git", "url": "https://github.com/nestjs/nest.git", "directory": "packages/common"}, "publishConfig": {"access": "public"}, "license": "MIT", "dependencies": {"file-type": "21.0.0", "iterare": "1.2.1", "load-esm": "1.0.2", "tslib": "2.8.1", "uid": "2.0.2"}, "peerDependencies": {"class-transformer": ">=0.4.1", "class-validator": ">=0.13.2", "reflect-metadata": "^0.1.12 || ^0.2.0", "rxjs": "^7.1.0"}, "peerDependenciesMeta": {"class-validator": {"optional": true}, "class-transformer": {"optional": true}}}