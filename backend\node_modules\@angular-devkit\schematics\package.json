{"name": "@angular-devkit/schematics", "version": "19.2.8", "description": "Angular Schematics - Library", "main": "src/index.js", "typings": "src/index.d.ts", "keywords": ["Angular CLI", "Angular DevKit", "angular", "blueprints", "code generation", "devkit", "scaffolding", "schematics", "sdk", "template", "tooling"], "dependencies": {"@angular-devkit/core": "19.2.8", "jsonc-parser": "3.3.1", "magic-string": "0.30.17", "ora": "5.4.1", "rxjs": "7.8.1"}, "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "packageManager": "pnpm@9.15.6", "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli"}