{"name": "@angular-devkit/schematics-cli", "version": "19.2.8", "description": "Angular Schematics - CLI", "homepage": "https://github.com/angular/angular-cli", "bin": {"schematics": "./bin/schematics.js"}, "keywords": ["Angular CLI", "Angular DevKit", "angular", "blueprints", "code generation", "devkit", "scaffolding", "schematics", "sdk", "template", "tooling"], "schematics": "./collection.json", "dependencies": {"@angular-devkit/core": "19.2.8", "@angular-devkit/schematics": "19.2.8", "@inquirer/prompts": "7.3.2", "ansi-colors": "4.1.3", "symbol-observable": "4.0.0", "yargs-parser": "21.1.1"}, "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "packageManager": "pnpm@9.15.6", "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}}