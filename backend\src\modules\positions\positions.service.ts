import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Position } from './entities/position.entity';
import { CreatePositionDto } from './dto/create-position.dto';
import { UpdatePositionDto } from './dto/update-position.dto';
import { QueryPositionDto } from './dto/query-position.dto';

@Injectable()
export class PositionsService {
  constructor(
    @InjectRepository(Position)
    private positionRepository: Repository<Position>,
  ) {}

  async create(createPositionDto: CreatePositionDto, currentUserId?: number): Promise<Position> {
    // 检查编码是否已存在
    const existingPosition = await this.positionRepository.findOne({
      where: { code: createPositionDto.code },
    });
    if (existingPosition) {
      throw new ConflictException('岗位编码已存在');
    }

    // 如果指定了上级岗位，检查上级岗位是否存在
    if (createPositionDto.parentId && createPositionDto.parentId !== 0) {
      const parentPosition = await this.positionRepository.findOne({
        where: { id: createPositionDto.parentId },
      });
      if (!parentPosition) {
        throw new NotFoundException('上级岗位不存在');
      }
    }

    const position = this.positionRepository.create({
      ...createPositionDto,
      parentId: createPositionDto.parentId || 0,
      sortOrder: createPositionDto.sortOrder || 0,
      status: createPositionDto.status ?? 1,
      createdBy: currentUserId,
    });

    return this.positionRepository.save(position);
  }

  async findAll(queryDto?: QueryPositionDto): Promise<Position[]> {
    try {
      const queryBuilder = this.positionRepository
        .createQueryBuilder('position')
        .orderBy('position.sortOrder', 'ASC')
        .addOrderBy('position.createdAt', 'ASC');

      if (queryDto?.name) {
        queryBuilder.andWhere('position.name LIKE :name', { name: `%${queryDto.name}%` });
      }

      if (queryDto?.code) {
        queryBuilder.andWhere('position.code LIKE :code', { code: `%${queryDto.code}%` });
      }

      if (queryDto?.type) {
        queryBuilder.andWhere('position.type = :type', { type: queryDto.type });
      }

      if (queryDto?.status !== undefined) {
        queryBuilder.andWhere('position.status = :status', { status: queryDto.status });
      }

      if (queryDto?.parentId !== undefined) {
        queryBuilder.andWhere('position.parentId = :parentId', { parentId: queryDto.parentId });
      }

      if (queryDto?.departmentId !== undefined) {
        queryBuilder.andWhere('position.departmentId = :departmentId', { departmentId: queryDto.departmentId });
      }

      const positions = await queryBuilder.getMany();

      // 简化返回数据，避免循环引用
      return positions.map(position => ({
        ...position,
        id: Number(position.id),
        parentId: Number(position.parentId),
        departmentId: Number(position.departmentId),
        departmentName: '', // 暂时为空，后续可以通过单独查询获取
      }));
    } catch (error) {
      console.error('查询岗位列表失败:', error);
      throw error;
    }
  }

  async findTree(queryDto?: QueryPositionDto): Promise<Position[]> {
    const positions = await this.findAll(queryDto);
    return this.buildTree(positions);
  }

  async findOne(id: number): Promise<Position> {
    const position = await this.positionRepository.findOne({
      where: { id },
      relations: ['department', 'parent', 'children'],
    });

    if (!position) {
      throw new NotFoundException('岗位不存在');
    }

    return position;
  }

  async update(id: number, updatePositionDto: UpdatePositionDto, currentUserId?: number): Promise<Position> {
    const position = await this.findOne(id);

    // 检查编码是否已存在（排除当前岗位）
    if (updatePositionDto.code && updatePositionDto.code !== position.code) {
      const existingPosition = await this.positionRepository.findOne({
        where: { code: updatePositionDto.code },
      });
      if (existingPosition && existingPosition.id !== id) {
        throw new ConflictException('岗位编码已存在');
      }
    }

    // 如果指定了上级岗位，检查上级岗位是否存在且不能是自己或自己的子岗位
    if (updatePositionDto.parentId && updatePositionDto.parentId !== 0) {
      if (updatePositionDto.parentId === id) {
        throw new ConflictException('不能将自己设置为上级岗位');
      }

      const parentPosition = await this.positionRepository.findOne({
        where: { id: updatePositionDto.parentId },
      });
      if (!parentPosition) {
        throw new NotFoundException('上级岗位不存在');
      }

      // 检查是否会形成循环引用
      if (await this.isDescendant(updatePositionDto.parentId, id)) {
        throw new ConflictException('不能将子岗位设置为上级岗位');
      }
    }

    Object.assign(position, {
      ...updatePositionDto,
      updatedBy: currentUserId,
    });

    return this.positionRepository.save(position);
  }

  async remove(id: number): Promise<void> {
    const position = await this.findOne(id);

    // 检查是否有子岗位
    const children = await this.positionRepository.find({
      where: { parentId: id },
    });
    if (children.length > 0) {
      throw new ConflictException('存在子岗位，无法删除');
    }

    await this.positionRepository.remove(position);
  }

  async batchRemove(ids: number[]): Promise<void> {
    for (const id of ids) {
      await this.remove(id);
    }
  }

  // 构建树形结构
  private buildTree(positions: Position[], parentId = 0): Position[] {
    const tree: Position[] = [];

    positions.forEach(position => {
      // 确保类型一致性，将两边都转换为数字进行比较
      const positionParentId = Number(position.parentId);
      const currentParentId = Number(parentId);

      if (positionParentId === currentParentId) {
        const children = this.buildTree(positions, position.id);
        if (children.length > 0) {
          position.children = children;
        }
        tree.push(position);
      }
    });

    return tree;
  }

  // 检查是否为子孙节点
  private async isDescendant(ancestorId: number, descendantId: number): Promise<boolean> {
    const descendants = await this.getDescendants(descendantId);
    return descendants.some(desc => desc.id === ancestorId);
  }

  // 获取所有子孙节点
  private async getDescendants(parentId: number): Promise<Position[]> {
    const descendants: Position[] = [];
    const children = await this.positionRepository.find({
      where: { parentId },
    });

    for (const child of children) {
      descendants.push(child);
      const grandChildren = await this.getDescendants(child.id);
      descendants.push(...grandChildren);
    }

    return descendants;
  }
}
