{"version": 3, "file": "AsapScheduler.js", "sourceRoot": "", "sources": ["../../../../src/internal/scheduler/AsapScheduler.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD;IAAmC,iCAAc;IAAjD;;IAkCA,CAAC;IAjCQ,6BAAK,GAAZ,UAAa,MAAyB;QACpC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAUpB,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC;QAChC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAEpB,IAAA,OAAO,GAAK,IAAI,QAAT,CAAU;QACzB,IAAI,KAAU,CAAC;QACf,MAAM,GAAG,MAAM,IAAI,OAAO,CAAC,KAAK,EAAG,CAAC;QAEpC,GAAG;YACD,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBACxD,MAAM;aACP;SACF,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE;QAE5E,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QAErB,IAAI,KAAK,EAAE;YACT,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE;gBACxE,MAAM,CAAC,WAAW,EAAE,CAAC;aACtB;YACD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IACH,oBAAC;AAAD,CAAC,AAlCD,CAAmC,cAAc,GAkChD"}