<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';

import { Page } from '@vben/common-ui';
import { onMounted } from 'vue';

import { useVbenModal, VbenButton, type VbenFormProps } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import OrganizationModal from './components/OrganizationModal.vue';
import {
  getOrganizationTree,
  deleteOrganization,
  // batchDeleteOrganizations
} from './organization.api';
import {
  columns,
  searchFormSchema,
  getTypeLabel,
  getStatusLabel,
  buildTree
} from './organization.data';
import { showConform } from '#/utils/alert.js';
import { message } from 'ant-design-vue';

const [CreateModal, createModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: OrganizationModal,
  fullscreenButton: false,
  destroyOnClose: true,
  onClosed: () => {
    gridApi.query();
  }
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: searchFormSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  },
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const gridOptions: VxeGridProps = {
  columns,
  height: 'auto',
  treeConfig: {
    childrenField: 'children',
    expandAll: true,
    accordion: false,
  },
  proxyConfig: {
    ajax: {
      query: async (_: any, formValues: any) => {
        const data = await getOrganizationTree({
          ...formValues,
        });

        // 构建树形结构
        // const treeData = buildTree(data || []);

        return {
          total: data?.length || 0,
          items: data,
        };
      },
    },
  },
  rowConfig: {
    isHover: true,
  },
  checkboxConfig: {
    labelField: 'name',
    reserve: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 新增事件
 */
const handleAdd = (parentId?: number) => {
  createModalApi.setState({ parentId });
  createModalApi.open();
};

/**
 * 编辑事件
 */
function handleEdit(row: any) {
  createModalApi.setState({ row, isEdit: true });
  createModalApi.open();
}

/**
 * 删除事件
 */
async function handleDelete(record: any) {
  showConform('提示', '确定要删除该组织吗？删除后不可恢复！', async () => {
    try {
      await deleteOrganization(record.id);
      message.success('删除成功');
      onUpdate();
    } catch (error: any) {
      message.error(error.message || '删除失败');
    }
  });
}

/**
 * 批量删除事件
 */
async function handleBatchDelete() {
  // 由于VXE表格的API可能不同，这里先注释掉批量删除功能
  message.info('批量删除功能待完善');
  // const selectRecords = gridApi.getCheckboxRecords();
  // if (selectRecords.length === 0) {
  //   message.warning('请选择要删除的数据');
  //   return;
  // }

  // showConform('提示', `确定要删除选中的 ${selectRecords.length} 条数据吗？删除后不可恢复！`, async () => {
  //   try {
  //     const ids = selectRecords.map((record: any) => record.id);
  //     await batchDeleteOrganizations(ids);
  //     message.success('批量删除成功');
  //     onUpdate();
  //   } catch (error: any) {
  //     message.error(error.message || '批量删除失败');
  //   }
  // });
}

/**
 * 添加子组织
 */
function handleAddChild(row: any) {
  handleAdd(row.id);
}

const onUpdate = () => {
  gridApi.reload();
};

// Fetch data on component mounted
onMounted(() => {
  // 初始化数据
});
</script>

<template>
  <Page auto-content-height>
    <!--引用表格-->
    <Grid>
      <!--插槽:table标题-->
      <template #toolbar-tools>
        <VbenButton
          pre-icon="ant-design:plus-outlined"
          type="primary"
          @click="handleAdd()"
        >
          新增组织
        </VbenButton>
      </template>

      <!-- 类别插槽 -->
      <template #type="{ row }">
        <span>{{ getTypeLabel(row.type) }}</span>
      </template>

      <!-- 状态插槽 -->
      <template #status="{ row }">
        <a-tag :color="row.status === 1 ? 'green' : 'red'">
          {{ getStatusLabel(row.status) }}
        </a-tag>
      </template>

      <!--操作栏-->
      <template #action="{ row }">
        <div class="actionBar">
          <a-button
            class="actionButton"
            type="link"
            @click="handleEdit(row)"
          >
            编辑
          </a-button>
          <a-button
            class="actionButton"
            type="link"
            @click="handleAddChild(row)"
          >
            添加子组织
          </a-button>
          <a-button
            class="actionButton"
            type="link"
            @click="handleDelete(row)"
          >
            删除
          </a-button>
        </div>
      </template>
    </Grid>

    <CreateModal @success="onUpdate" />
  </Page>
</template>

<style scoped lang="less">
@import '#/styles/dark-antd.less';

.actionButton {
  padding: 6px;
  color: hsl(var(--primary));
}

.actionButton:hover {
  color: hsl(var(--primary));
}

.actionBar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
</style>
