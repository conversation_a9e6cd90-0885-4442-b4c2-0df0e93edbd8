// `parsePhoneNumber()` named export has been renamed to `parsePhoneNumberWithError()`.
export { parsePhoneNumberWithError, parsePhoneNumberWithError as parsePhoneNumber } from './exports/parsePhoneNumberWithError.js'
// `parsePhoneNumberFromString()` named export is now considered legacy:
// it has been promoted to a default export due to being too verbose.
export { parsePhoneNumber as parsePhoneNumberFromString, parsePhoneNumber as default } from './exports/parsePhoneNumber.js'

export { isValidPhoneNumber } from './exports/isValidPhoneNumber.js'
export { isPossiblePhoneNumber } from './exports/isPossiblePhoneNumber.js'
export { validatePhoneNumberLength } from './exports/validatePhoneNumberLength.js'

// Deprecated.
export { findNumbers } from './exports/findNumbers.js'
export { searchNumbers } from './exports/searchNumbers.js'

export { findPhoneNumbersInText } from './exports/findPhoneNumbersInText.js'
export { searchPhoneNumbersInText } from './exports/searchPhoneNumbersInText.js'
export { PhoneNumberMatcher } from './exports/PhoneNumberMatcher.js'

export { AsYouType } from './exports/AsYouType.js'

export { isSupportedCountry } from './exports/isSupportedCountry.js'
export { getCountries } from './exports/getCountries.js'
export { getCountryCallingCode } from './exports/getCountryCallingCode.js'
export { getExtPrefix } from './exports/getExtPrefix.js'

export { Metadata } from './exports/Metadata.js'
export { getExampleNumber } from './exports/getExampleNumber.js'

export { formatIncompletePhoneNumber } from './exports/formatIncompletePhoneNumber.js'
export { PhoneNumber } from './exports/PhoneNumber.js'

export {
	ParseError,
	parseIncompletePhoneNumber,
	parsePhoneNumberCharacter,
	parseDigits,
	parseRFC3966,
	formatRFC3966,
	DIGIT_PLACEHOLDER
} from '../core/index.js'
