"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const bcrypt = require("bcryptjs");
const user_entity_1 = require("./entities/user.entity");
const pagination_dto_1 = require("../../common/dto/pagination.dto");
let UsersService = class UsersService {
    constructor(userRepository) {
        this.userRepository = userRepository;
    }
    async create(createUserDto, currentUserId) {
        const existingUser = await this.userRepository.findOne({
            where: { username: createUserDto.username },
        });
        if (existingUser) {
            throw new common_1.ConflictException('用户名已存在');
        }
        if (createUserDto.employeeId) {
            const existingEmployee = await this.userRepository.findOne({
                where: { employeeId: createUserDto.employeeId },
            });
            if (existingEmployee) {
                throw new common_1.ConflictException('工号已存在');
            }
        }
        const hashedPassword = await bcrypt.hash(createUserDto.password, 10);
        const user = this.userRepository.create({
            ...createUserDto,
            password: hashedPassword,
            createdBy: currentUserId,
        });
        return this.userRepository.save(user);
    }
    async findAll(queryDto) {
        const { page = 1, pageSize = 10, username, realName, status, departmentId, positionId } = queryDto;
        const queryBuilder = this.userRepository.createQueryBuilder('user')
            .leftJoinAndSelect('user.department', 'department')
            .leftJoinAndSelect('user.position', 'position')
            .leftJoinAndSelect('user.roles', 'roles');
        if (username) {
            queryBuilder.andWhere('user.username LIKE :username', { username: `%${username}%` });
        }
        if (realName) {
            queryBuilder.andWhere('user.realName LIKE :realName', { realName: `%${realName}%` });
        }
        if (status !== undefined) {
            queryBuilder.andWhere('user.status = :status', { status });
        }
        if (departmentId) {
            queryBuilder.andWhere('user.departmentId = :departmentId', { departmentId });
        }
        if (positionId) {
            queryBuilder.andWhere('user.positionId = :positionId', { positionId });
        }
        const [list, total] = await queryBuilder
            .skip((page - 1) * pageSize)
            .take(pageSize)
            .getManyAndCount();
        return new pagination_dto_1.PaginationResult(list, total, page, pageSize);
    }
    async findOne(id) {
        const user = await this.userRepository.findOne({
            where: { id },
            relations: ['department', 'position', 'roles'],
        });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        return user;
    }
    async findById(id) {
        return this.userRepository.findOne({
            where: { id },
            relations: ['department', 'position', 'roles'],
        });
    }
    async findByUsername(username) {
        return this.userRepository.findOne({
            where: { username },
            relations: ['department', 'position', 'roles'],
        });
    }
    async findByPhone(phone) {
        return this.userRepository.findOne({
            where: { phone },
            relations: ['department', 'position', 'roles'],
        });
    }
    async update(id, updateUserDto, currentUserId) {
        const user = await this.findOne(id);
        if (updateUserDto.employeeId && updateUserDto.employeeId !== user.employeeId) {
            const existingEmployee = await this.userRepository.findOne({
                where: { employeeId: updateUserDto.employeeId },
            });
            if (existingEmployee && existingEmployee.id !== id) {
                throw new common_1.ConflictException('工号已存在');
            }
        }
        Object.assign(user, {
            ...updateUserDto,
            updatedBy: currentUserId,
        });
        return this.userRepository.save(user);
    }
    async remove(id) {
        const user = await this.findOne(id);
        await this.userRepository.remove(user);
    }
    async batchRemove(ids) {
        await this.userRepository.delete(ids);
    }
    async resetPassword(id, newPassword, currentUserId) {
        const user = await this.findOne(id);
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        user.password = hashedPassword;
        user.updatedBy = currentUserId;
        await this.userRepository.save(user);
    }
    async updateStatus(id, status, currentUserId) {
        const user = await this.findOne(id);
        user.status = status;
        user.updatedBy = currentUserId;
        await this.userRepository.save(user);
    }
    async updateLastLoginTime(id) {
        await this.userRepository.update(id, {
            lastLoginTime: new Date(),
        });
    }
    async validatePassword(user, password) {
        return bcrypt.compare(password, user.password);
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], UsersService);
//# sourceMappingURL=users.service.js.map