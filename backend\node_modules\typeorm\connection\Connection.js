"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Connection = void 0;
const DataSource_1 = require("../data-source/DataSource");
/**
 * Connection is a single database ORM connection to a specific database.
 * Its not required to be a database connection, depend on database type it can create connection pool.
 * You can have multiple connections to multiple databases in your application.
 *
 * @deprecated
 */
class Connection extends DataSource_1.DataSource {
}
exports.Connection = Connection;

//# sourceMappingURL=Connection.js.map
