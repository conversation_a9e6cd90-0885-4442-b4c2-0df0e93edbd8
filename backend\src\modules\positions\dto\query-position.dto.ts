import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsEnum } from 'class-validator';

export class QueryPositionDto {
  @ApiProperty({ description: '岗位名称', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: '岗位编码', required: false })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({ description: '岗位类别：department部门，office科室', required: false })
  @IsOptional()
  @IsEnum(['department', 'office'])
  type?: string;

  @ApiProperty({ description: '状态：1启用，0停用', required: false })
  @IsOptional()
  @IsNumber()
  status?: number;

  @ApiProperty({ description: '上级岗位ID', required: false })
  @IsOptional()
  @IsNumber()
  parentId?: number;

  @ApiProperty({ description: '归属部门ID', required: false })
  @IsOptional()
  @IsNumber()
  departmentId?: number;
}
