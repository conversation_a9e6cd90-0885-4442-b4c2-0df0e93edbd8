<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';
import { VbenTree, Loading, Fallback } from '@vben/common-ui';
import { getOrganizationTree } from '../organization.api';

// 定义组件属性
interface Props {
  selectedOrganizationId?: number | null;
}

// 定义事件
interface Emits {
  (e: 'select', organizationId: number | null, organizationName: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  selectedOrganizationId: null,
});

const emit = defineEmits<Emits>();

// 响应式数据
const organizationTreeData = ref([]);
const selectedKeys = ref<number[]>([]);
const expandedKeys = ref<number[]>([]);
const loading = ref(false);
const error = ref('');

// 加载组织树数据
const loadOrganizationTree = async () => {
  try {
    loading.value = true;
    error.value = '';
    const data = await getOrganizationTree();
    organizationTreeData.value = data || [];

    // 默认展开所有节点
    const allKeys = getAllKeys(data || []);
    expandedKeys.value = allKeys;
  } catch (err) {
    console.error('加载组织树失败:', err);
    error.value = '加载组织数据失败，请重试';
  } finally {
    loading.value = false;
  }
};

// 获取所有节点的key
const getAllKeys = (data: any[]): number[] => {
  const keys: number[] = [];
  const traverse = (nodes: any[]) => {
    nodes.forEach(node => {
      if (node.id !== null && node.id !== undefined) {
        keys.push(node.id);
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  traverse(data);
  return keys;
};

// VbenTree的选择处理
const handleTreeSelect = (item: any) => {
  const selectedKey = item.value?.id;
  selectedKeys.value = selectedKey ? [selectedKey] : [];

  if (selectedKey) {
    emit('select', selectedKey, item.value?.name || '');
  } else {
    emit('select', null, '');
  }
};

// 根据ID查找节点
const findNodeById = (nodes: any[], id: number): any => {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

// 处理展开/收起
const handleExpand = (item: any) => {
  // VbenTree的expand事件处理
  console.log('Tree expand:', item);
};



// 清除选择
const clearSelection = () => {
  selectedKeys.value = [];
  emit('select', null, '');
};

// 监听外部选择变化
watch(() => props.selectedOrganizationId, (newVal) => {
  if (newVal) {
    selectedKeys.value = [newVal];
  } else {
    selectedKeys.value = [];
  }
}, { immediate: true });



// 组件挂载时加载数据
onMounted(() => {
  loadOrganizationTree();
});



// 暴露方法给父组件
defineExpose({
  loadOrganizationTree,
  clearSelection,
});
</script>

<template>
  <div class="organization-sidebar">
    <!-- 标题栏 -->
    <div class="sidebar-header">
      <div class="header-title">
        <span class="title-text">组织架构</span>
      </div>
    </div>

    <!-- 组织树 -->
    <div class="sidebar-content">
      <Loading :spinning="loading">
        <VbenTree
          v-if="organizationTreeData.length > 0"
          v-model="selectedKeys"
          :tree-data="organizationTreeData"
          :expanded="expandedKeys"
          :default-expanded-level="10"
          label-field="name"
          value-field="id"
          children-field="children"
          :show-icon="false"
          @select="handleTreeSelect"
          @expand="handleExpand"
        >
          <template #node="{ value }">
            <div
              class="tree-node-title"
              :class="{ 'selected': selectedKeys.includes(value.id) }"
            >
              <span class="node-name">{{ value.name }}</span>
              <span
                v-if="selectedKeys.includes(value.id)"
                class="selected-dot"
              ></span>
            </div>
          </template>
        </VbenTree>

        <div v-else-if="error" class="error-state">
          <Fallback
            status="500"
            :title="error"
            description="请检查网络连接或联系管理员"
          >
            <template #action>
              <VbenButton @click="loadOrganizationTree">
                重新加载
              </VbenButton>
            </template>
          </Fallback>
        </div>

        <div v-else-if="!loading" class="empty-state">
          <div class="flex flex-col items-center justify-center py-8">
            <div class="text-muted-foreground text-sm">暂无组织数据</div>
          </div>
        </div>
      </Loading>
    </div>
  </div>
</template>

<style scoped>
/* 组件内部样式 */
.organization-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: hsl(var(--background));
  border-right: 1px solid hsl(var(--border));
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid hsl(var(--border));
  flex-shrink: 0;
}

.header-title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.title-text {
  font-size: 1rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

.sidebar-content {
  flex: 1;
  padding: 0.5rem;
  overflow-y: auto;
}

.tree-node-title {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease-in-out;
  position: relative;
}

.tree-node-title:hover {
  background-color: hsl(var(--accent));
}

.tree-node-title.selected {
  background-color: hsl(var(--primary) / 0.15);
  color: hsl(var(--primary));
  font-weight: 500;
  border: 1px solid hsl(var(--primary) / 0.3);
}

.tree-node-title.selected:hover {
  background-color: hsl(var(--primary) / 0.2);
}

.node-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.875rem;
}

.selected-dot {
  width: 6px;
  height: 6px;
  background: hsl(var(--primary));
  border-radius: 50%;
  opacity: 0.8;
  margin-left: 8px;
  flex-shrink: 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 12rem;
}

.error-state {
  padding: 1.25rem;
  text-align: center;
}

/* VbenTree 样式覆盖 */
:deep(.vben-tree) {
  background: transparent;
}

:deep(.vben-tree .tree-node) {
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  margin: 2px 0;
  padding: 4px 8px;
}

:deep(.vben-tree .tree-node:hover) {
  background-color: hsl(var(--accent));
}

/* 选中状态样式 */
:deep(.vben-tree .tree-node[data-selected="true"]) {
  background-color: hsl(var(--primary) / 0.15);
  color: hsl(var(--primary));
  font-weight: 500;
  border: 1px solid hsl(var(--primary) / 0.3);
  position: relative;
}

:deep(.vben-tree .tree-node[data-selected="true"]:hover) {
  background-color: hsl(var(--primary) / 0.2);
}

/* 选中状态的点指示器 */
:deep(.vben-tree .tree-node[data-selected="true"]::after) {
  content: '';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: hsl(var(--primary));
  border-radius: 50%;
  opacity: 0.8;
}
</style>


