<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';
import { VbenTree, Loading, Fallback } from '@vben/common-ui';
import { getOrganizationTree } from '../organization.api';

// 定义组件属性
interface Props {
  selectedOrganizationId?: number | null;
}

// 定义事件
interface Emits {
  (e: 'select', organizationId: number | null, organizationName: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  selectedOrganizationId: null,
});

const emit = defineEmits<Emits>();

// 响应式数据
const organizationTreeData = ref([]);
const selectedKeys = ref<number[]>([]);
const expandedKeys = ref<number[]>([]);
const loading = ref(false);
const error = ref('');

// 加载组织树数据
const loadOrganizationTree = async () => {
  try {
    loading.value = true;
    error.value = '';
    const data = await getOrganizationTree();
    organizationTreeData.value = data || [];

    // 默认展开所有节点
    const allKeys = getAllKeys(data || []);
    expandedKeys.value = allKeys;
  } catch (err) {
    console.error('加载组织树失败:', err);
    error.value = '加载组织数据失败，请重试';
  } finally {
    loading.value = false;
  }
};

// 获取所有节点的key
const getAllKeys = (data: any[]): number[] => {
  const keys: number[] = [];
  const traverse = (nodes: any[]) => {
    nodes.forEach(node => {
      if (node.id !== null && node.id !== undefined) {
        keys.push(node.id);
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  traverse(data);
  return keys;
};

// VbenTree的选择处理
const handleTreeSelect = (item: any) => {
  const selectedKey = item.value?.id;

  // 如果点击的是已选中的节点，则取消选择
  if (selectedKeys.value.includes(selectedKey)) {
    selectedKeys.value = [];
    emit('select', null, '');
    return;
  }

  selectedKeys.value = selectedKey ? [selectedKey] : [];

  if (selectedKey) {
    emit('select', selectedKey, item.value?.name || '');

    // 确保选中的节点可见（展开父节点）
    ensureNodeVisible(selectedKey);
  } else {
    emit('select', null, '');
  }
};

// 确保节点可见（展开所有父节点）
const ensureNodeVisible = (nodeId: number) => {
  const parentIds = getParentIds(organizationTreeData.value, nodeId);
  const newExpandedKeys = [...new Set([...expandedKeys.value, ...parentIds])];
  expandedKeys.value = newExpandedKeys;
};

// 获取节点的所有父节点ID
const getParentIds = (nodes: any[], targetId: number, parentIds: number[] = []): number[] => {
  for (const node of nodes) {
    if (node.id === targetId) {
      return parentIds;
    }
    if (node.children && node.children.length > 0) {
      const found = getParentIds(node.children, targetId, [...parentIds, node.id]);
      if (found.length > parentIds.length || found.some((id: number) => !parentIds.includes(id))) {
        return found;
      }
    }
  }
  return parentIds;
};

// 根据ID查找节点
const findNodeById = (nodes: any[], id: number): any => {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

// 处理展开/收起
const handleExpand = (item: any) => {
  // VbenTree的expand事件处理
  console.log('Tree expand:', item);
};



// 清除选择
const clearSelection = () => {
  selectedKeys.value = [];
  emit('select', null, '');
};

// 程序化设置选中状态
const setSelectedOrganization = (organizationId: number | null) => {
  if (organizationId) {
    selectedKeys.value = [organizationId];
    ensureNodeVisible(organizationId);

    // 查找节点名称
    const node = findNodeById(organizationTreeData.value, organizationId);
    emit('select', organizationId, node?.name || '');
  } else {
    clearSelection();
  }
};

// 监听外部选择变化
watch(() => props.selectedOrganizationId, (newVal, oldVal) => {
  // 避免重复设置相同的值
  if (newVal !== oldVal) {
    if (newVal) {
      selectedKeys.value = [newVal];
      // 确保选中的节点可见
      if (organizationTreeData.value.length > 0) {
        ensureNodeVisible(newVal);
      }
    } else {
      selectedKeys.value = [];
    }
  }
}, { immediate: true });



// 组件挂载时加载数据
onMounted(() => {
  loadOrganizationTree();
});



// 暴露方法给父组件
defineExpose({
  loadOrganizationTree,
  clearSelection,
  setSelectedOrganization,
  findNodeById,
});
</script>

<template>
  <div class="organization-sidebar">
    <!-- 标题栏 -->
    <div class="sidebar-header">
      <div class="header-title">
        <span class="title-text">组织架构</span>
      </div>
    </div>

    <!-- 组织树 -->
    <div class="sidebar-content">
      <Loading :spinning="loading">
        <VbenTree
          v-if="organizationTreeData.length > 0"
          v-model="selectedKeys"
          :tree-data="organizationTreeData"
          :expanded="expandedKeys"
          :default-expanded-level="10"
          label-field="name"
          value-field="id"
          children-field="children"
          :show-icon="false"
          class="organization-tree"
          @select="handleTreeSelect"
          @expand="handleExpand"
        >
          <template #node="{ value }">
            <div
              class="tree-node-content"
              :class="{
                'is-selected': selectedKeys.includes(value.id),
                'has-children': value.children && value.children.length > 0
              }"
            >
              <span class="node-name">{{ value.name }}</span>
              <div
                v-if="selectedKeys.includes(value.id)"
                class="selected-indicator"
              >
                <div class="selected-dot"></div>
              </div>
            </div>
          </template>
        </VbenTree>

        <div v-else-if="error" class="error-state">
          <Fallback
            status="500"
            :title="error"
            description="请检查网络连接或联系管理员"
          >
            <template #action>
              <VbenButton @click="loadOrganizationTree">
                重新加载
              </VbenButton>
            </template>
          </Fallback>
        </div>

        <div v-else-if="!loading" class="empty-state">
          <div class="flex flex-col items-center justify-center py-8">
            <div class="text-muted-foreground text-sm">暂无组织数据</div>
          </div>
        </div>
      </Loading>
    </div>
  </div>
</template>

<style scoped>
/* 组件内部样式 */
.organization-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: hsl(var(--background));
  border-right: 1px solid hsl(var(--border));
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid hsl(var(--border));
  flex-shrink: 0;
}

.header-title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.title-text {
  font-size: 1rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

.sidebar-content {
  flex: 1;
  padding: 0.5rem;
  overflow-y: auto;
}

/* 树节点内容样式 */
.tree-node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  cursor: pointer;
  min-height: 36px;
}

.tree-node-content:hover {
  background-color: hsl(var(--accent) / 0.8);
  transform: translateX(2px);
}

.tree-node-content.is-selected {
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.12) 0%,
    hsl(var(--primary) / 0.18) 100%);
  color: hsl(var(--primary));
  font-weight: 600;
  border: 1px solid hsl(var(--primary) / 0.25);
  box-shadow: 0 2px 8px hsl(var(--primary) / 0.15);
}

.tree-node-content.is-selected:hover {
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.18) 0%,
    hsl(var(--primary) / 0.25) 100%);
  transform: translateX(3px);
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.2);
}

.node-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.875rem;
  line-height: 1.4;
  transition: color 0.2s ease;
}

.tree-node-content.is-selected .node-name {
  color: hsl(var(--primary));
}

/* 选中指示器容器 */
.selected-indicator {
  display: flex;
  align-items: center;
  margin-left: 8px;
  animation: fadeInScale 0.3s ease-out;
}

.selected-dot {
  width: 8px;
  height: 8px;
  background: hsl(var(--primary));
  border-radius: 50%;
  position: relative;
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
}

.selected-dot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background: hsl(var(--background));
  border-radius: 50%;
}

/* 动画效果 */
@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 12rem;
}

.error-state {
  padding: 1.25rem;
  text-align: center;
}

/* VbenTree 组件样式优化 */
:deep(.vben-tree) {
  background: transparent;
  font-family: inherit;
}

:deep(.vben-tree .tree-node) {
  border-radius: 8px;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  margin: 3px 0;
  padding: 0;
  background: transparent;
  border: none;
}

:deep(.vben-tree .tree-node:hover) {
  background: transparent;
}

/* 移除默认的选中样式，使用自定义样式 */
:deep(.vben-tree .tree-node[data-selected="true"]) {
  background: transparent;
  color: inherit;
  font-weight: inherit;
  border: none;
}

:deep(.vben-tree .tree-node[data-selected="true"]:hover) {
  background: transparent;
}

/* 展开/收起图标样式 */
:deep(.vben-tree .tree-node-expand-icon) {
  color: hsl(var(--muted-foreground));
  transition: all 0.2s ease;
  margin-right: 4px;
}

:deep(.vben-tree .tree-node-expand-icon:hover) {
  color: hsl(var(--foreground));
}

/* 层级缩进优化 */
:deep(.vben-tree .tree-node-indent) {
  width: 20px;
}

/* 焦点状态优化 */
:deep(.vben-tree .tree-node:focus-visible) {
  outline: 2px solid hsl(var(--primary) / 0.5);
  outline-offset: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tree-node-content {
    padding: 6px 10px;
    min-height: 32px;
  }

  .node-name {
    font-size: 0.8rem;
  }

  .selected-dot {
    width: 6px;
    height: 6px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .tree-node-content.is-selected {
    box-shadow: 0 2px 8px hsl(var(--primary) / 0.25);
  }

  .tree-node-content.is-selected:hover {
    box-shadow: 0 4px 12px hsl(var(--primary) / 0.3);
  }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  .tree-node-content,
  .selected-indicator,
  :deep(.vben-tree .tree-node),
  :deep(.vben-tree .tree-node-expand-icon) {
    transition: none;
  }

  .selected-indicator {
    animation: none;
  }

  .tree-node-content:hover,
  .tree-node-content.is-selected:hover {
    transform: none;
  }
}
</style>


