import { Controller, Post, Body, UseGuards, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { LoginDto, LoginResponseDto } from './dto/login.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { User } from '@/modules/users/entities/user.entity';
import { ResponseDto } from '@/common/dto/response.dto';

@ApiTags('认证')
@Controller('system')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @ApiOperation({ summary: '用户登录' })
  @ApiResponse({ status: 200, description: '登录成功', type: LoginResponseDto })
  async login(@Body() loginDto: LoginDto): Promise<ResponseDto<LoginResponseDto>> {
    const result = await this.authService.login(loginDto);
    return ResponseDto.success(result, '登录成功');
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '用户退出' })
  @ApiResponse({ status: 200, description: '退出成功' })
  async logout(@CurrentUser() user: User): Promise<ResponseDto<any>> {
    const result = await this.authService.logout(user);
    return ResponseDto.success(result, '退出成功');
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取用户信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getProfile(@CurrentUser() user: User): Promise<ResponseDto<any>> {
    const result = await this.authService.getProfile(user.id);
    return ResponseDto.success(result, '获取成功');
  }
}
