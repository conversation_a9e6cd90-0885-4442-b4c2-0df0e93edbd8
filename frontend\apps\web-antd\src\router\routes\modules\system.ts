import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ion:settings-outline',
      order: 1000,
      title: '系统管理',
      hideChildrenInMenu: false,
    },
    name: 'System',
    path: '/system',
    children: [
      {
        name: 'Organization',
        path: '/system/organization',
        component: () => import('#/views/system/organization/index.vue'),
        meta: {
          icon: 'ion:business-outline',
          title: '组织管理',
          order: 1,
          keepAlive: true,
          hideInBreadcrumb: false,
        },
      },
      {
        name: 'Position',
        path: '/system/position',
        component: () => import('#/views/system/position/index.vue'),
        meta: {
          icon: 'ion:briefcase-outline',
          title: '岗位管理',
          order: 2,
          keepAlive: true,
          hideInBreadcrumb: false,
        },
      },
    ],
  },
];

export default routes;
