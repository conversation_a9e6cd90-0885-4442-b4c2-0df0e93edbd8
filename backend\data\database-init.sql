-- 创建数据库
CREATE DATABASE IF NOT EXISTS system_manage DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE system_manage;

-- 用户表
CREATE TABLE users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  employee_id VARCHAR(50) UNIQUE COMMENT '工号',
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  password VARCHAR(255) NOT NULL COMMENT '密码',
  real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
  phone VARCHAR(20) COMMENT '手机号',
  email VARCHAR(100) COMMENT '邮箱',
  department_id BIGINT COMMENT '部门ID',
  position_id BIGINT COMMENT '岗位ID',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
  avatar VARCHAR(255) COMMENT '头像',
  last_login_time DATETIME COMMENT '最后登录时间',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by BIGINT COMMENT '创建人',
  updated_by BIGINT COMMENT '更新人',
  INDEX idx_username (username),
  INDEX idx_department_id (department_id),
  INDEX idx_position_id (position_id),
  INDEX idx_status (status)
);

-- 组织机构表
CREATE TABLE organizations (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '机构名称',
  code VARCHAR(50) UNIQUE NOT NULL COMMENT '机构编码',
  parent_id BIGINT DEFAULT 0 COMMENT '上级机构ID，0为根节点',
  type ENUM('department', 'office') DEFAULT 'department' COMMENT '类别：department部门，office科室',
  sort_order INT DEFAULT 0 COMMENT '排序',
  default_role_id BIGINT COMMENT '默认角色ID',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0停用',
  description TEXT COMMENT '描述',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by BIGINT COMMENT '创建人',
  updated_by BIGINT COMMENT '更新人',
  INDEX idx_parent_id (parent_id),
  INDEX idx_code (code),
  INDEX idx_status (status),
  INDEX idx_sort_order (sort_order)
);

-- 岗位表
CREATE TABLE positions (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '岗位名称',
  code VARCHAR(50) UNIQUE NOT NULL COMMENT '岗位编码',
  parent_id BIGINT DEFAULT 0 COMMENT '主管上级岗位ID',
  department_id BIGINT NOT NULL COMMENT '归属部门ID',
  type ENUM('department', 'office') DEFAULT 'department' COMMENT '类别：department部门，office科室',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0停用',
  description TEXT COMMENT '岗位描述',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by BIGINT COMMENT '创建人',
  updated_by BIGINT COMMENT '更新人',
  INDEX idx_parent_id (parent_id),
  INDEX idx_department_id (department_id),
  INDEX idx_code (code),
  INDEX idx_status (status),
  INDEX idx_sort_order (sort_order)
);

-- 角色表
CREATE TABLE roles (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '角色名称',
  code VARCHAR(50) UNIQUE NOT NULL COMMENT '角色编码',
  description TEXT COMMENT '角色描述',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by BIGINT COMMENT '创建人',
  updated_by BIGINT COMMENT '更新人',
  INDEX idx_code (code),
  INDEX idx_status (status)
);

-- 用户组表
CREATE TABLE user_groups (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '用户组名称',
  description TEXT COMMENT '用户组描述',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by BIGINT COMMENT '创建人',
  updated_by BIGINT COMMENT '更新人',
  INDEX idx_status (status)
);

-- 用户组成员表
CREATE TABLE user_group_members (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_group_id BIGINT NOT NULL COMMENT '用户组ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by BIGINT COMMENT '创建人',
  UNIQUE KEY uk_group_user (user_group_id, user_id),
  INDEX idx_user_group_id (user_group_id),
  INDEX idx_user_id (user_id)
);

-- 用户组角色关联表
CREATE TABLE user_group_roles (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_group_id BIGINT NOT NULL COMMENT '用户组ID',
  role_id BIGINT NOT NULL COMMENT '角色ID',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by BIGINT COMMENT '创建人',
  UNIQUE KEY uk_group_role (user_group_id, role_id),
  INDEX idx_user_group_id (user_group_id),
  INDEX idx_role_id (role_id)
);

-- 应用表
CREATE TABLE applications (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '应用名称',
  app_key VARCHAR(100) UNIQUE NOT NULL COMMENT '应用Key',
  contact_person VARCHAR(100) COMMENT '联系人',
  contact_phone VARCHAR(20) COMMENT '联系电话',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
  description TEXT COMMENT '应用描述',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by BIGINT COMMENT '创建人',
  updated_by BIGINT COMMENT '更新人',
  INDEX idx_app_key (app_key),
  INDEX idx_status (status)
);

-- 功能表
CREATE TABLE functions (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  application_id BIGINT NOT NULL COMMENT '应用ID',
  parent_id BIGINT DEFAULT 0 COMMENT '上级功能ID，0为根节点',
  name VARCHAR(100) NOT NULL COMMENT '功能名称',
  type ENUM('directory', 'menu', 'button') DEFAULT 'menu' COMMENT '功能类型：directory目录，menu菜单，button按钮',
  path VARCHAR(255) COMMENT '功能路径',
  icon VARCHAR(100) COMMENT '功能图标',
  resource VARCHAR(255) COMMENT '关联资源',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0停用',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by BIGINT COMMENT '创建人',
  updated_by BIGINT COMMENT '更新人',
  INDEX idx_application_id (application_id),
  INDEX idx_parent_id (parent_id),
  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_sort_order (sort_order)
);

-- 角色功能关联表
CREATE TABLE role_functions (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  role_id BIGINT NOT NULL COMMENT '角色ID',
  function_id BIGINT NOT NULL COMMENT '功能ID',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by BIGINT COMMENT '创建人',
  UNIQUE KEY uk_role_function (role_id, function_id),
  INDEX idx_role_id (role_id),
  INDEX idx_function_id (function_id)
);

-- 用户角色关联表
CREATE TABLE user_roles (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL COMMENT '用户ID',
  role_id BIGINT NOT NULL COMMENT '角色ID',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by BIGINT COMMENT '创建人',
  UNIQUE KEY uk_user_role (user_id, role_id),
  INDEX idx_user_id (user_id),
  INDEX idx_role_id (role_id)
);

-- 字典类型表
CREATE TABLE dict_types (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '字典名称',
  type VARCHAR(100) UNIQUE NOT NULL COMMENT '字典类型',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0停用',
  remark TEXT COMMENT '备注',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by BIGINT COMMENT '创建人',
  updated_by BIGINT COMMENT '更新人',
  INDEX idx_type (type),
  INDEX idx_status (status)
);

-- 字典数据表
CREATE TABLE dict_data (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  dict_type VARCHAR(100) NOT NULL COMMENT '字典类型',
  dict_label VARCHAR(100) NOT NULL COMMENT '字典标签',
  dict_value VARCHAR(100) NOT NULL COMMENT '字典键值',
  parent_id BIGINT DEFAULT 0 COMMENT '父级ID，支持层级结构',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0停用',
  is_default TINYINT DEFAULT 0 COMMENT '是否默认：1是，0否',
  remark TEXT COMMENT '备注',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by BIGINT COMMENT '创建人',
  updated_by BIGINT COMMENT '更新人',
  INDEX idx_dict_type (dict_type),
  INDEX idx_parent_id (parent_id),
  INDEX idx_status (status),
  INDEX idx_sort_order (sort_order)
);

-- 系统配置表
CREATE TABLE sys_config (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  config_name VARCHAR(100) NOT NULL COMMENT '参数名称',
  config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '参数键名',
  config_value TEXT COMMENT '参数键值',
  config_type ENUM('Y', 'N') DEFAULT 'N' COMMENT '系统内置：Y是，N否',
  remark TEXT COMMENT '备注',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by BIGINT COMMENT '创建人',
  updated_by BIGINT COMMENT '更新人',
  INDEX idx_config_key (config_key),
  INDEX idx_config_type (config_type)
);

-- 插入初始数据
-- 默认角色
INSERT INTO roles (name, code, description, status) VALUES
('超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', 1),
('系统管理员', 'admin', '系统管理员，拥有大部分管理权限', 1),
('普通用户', 'user', '普通用户，拥有基础功能权限', 1);

-- 默认用户（密码为admin123）
INSERT INTO users (employee_id, username, password, real_name, status) VALUES
('admin', 'admin', '$2b$10$MvaLUvYvSfRqMKVd.Tz0c.2j3U0LG2OnN0xrXIboiE/6bbZSU7BcG', '系统管理员', 1);

-- 测试用户（手机号登录，密码为8888a8888#@）
INSERT INTO users (employee_id, username, password, real_name, phone, status) VALUES
('test001', 'testuser', '$2b$10$4HoGJ.gGo7ffRDcu4E5DDub0qN5IvXiuuwUMMSVF2YiDeOtSgwVnm', '何', '15259630375', 1);

-- 分配超级管理员角色给admin用户
INSERT INTO user_roles (user_id, role_id) VALUES (1, 1);

-- 分配系统管理员角色给测试用户
INSERT INTO user_roles (user_id, role_id) VALUES (2, 2);

-- 默认应用
INSERT INTO applications (name, app_key, contact_person, status, description) VALUES
('系统管理中心', 'system_manage', '系统管理员', 1, '系统管理后台应用');

-- 默认字典类型
INSERT INTO dict_types (name, type, status, remark) VALUES
('用户状态', 'sys_user_status', 1, '用户状态列表'),
('菜单状态', 'sys_show_hide', 1, '菜单状态列表'),
('系统开关', 'sys_normal_disable', 1, '系统开关列表'),
('系统是否', 'sys_yes_no', 1, '系统是否列表'),
('系统状态', 'sys_common_status', 1, '登录状态列表');

-- 默认字典数据
INSERT INTO dict_data (dict_type, dict_label, dict_value, sort_order, status, is_default) VALUES
('sys_user_status', '正常', '1', 1, 1, 1),
('sys_user_status', '停用', '0', 2, 1, 0),
('sys_show_hide', '显示', '1', 1, 1, 1),
('sys_show_hide', '隐藏', '0', 2, 1, 0),
('sys_normal_disable', '正常', '1', 1, 1, 1),
('sys_normal_disable', '停用', '0', 2, 1, 0),
('sys_yes_no', '是', 'Y', 1, 1, 1),
('sys_yes_no', '否', 'N', 2, 1, 0);

-- 默认系统配置
INSERT INTO sys_config (config_name, config_key, config_value, config_type, remark) VALUES
('用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', '初始化密码 123456'),
('账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', '是否开启验证码功能（true开启，false关闭）'),
('账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', '是否开启注册用户功能（true开启，false关闭）');
