"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PositionsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const positions_service_1 = require("./positions.service");
const create_position_dto_1 = require("./dto/create-position.dto");
const update_position_dto_1 = require("./dto/update-position.dto");
const query_position_dto_1 = require("./dto/query-position.dto");
const position_entity_1 = require("./entities/position.entity");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
const response_dto_1 = require("../../common/dto/response.dto");
const user_entity_1 = require("../users/entities/user.entity");
let PositionsController = class PositionsController {
    constructor(positionsService) {
        this.positionsService = positionsService;
    }
    async create(createPositionDto, currentUser) {
        const position = await this.positionsService.create(createPositionDto, currentUser.id);
        return response_dto_1.ResponseDto.success(position, '创建成功');
    }
    async findAll(queryDto) {
        const positions = await this.positionsService.findAll(queryDto);
        return response_dto_1.ResponseDto.success(positions, '获取成功');
    }
    async findTree(queryDto) {
        const tree = await this.positionsService.findTree(queryDto);
        return response_dto_1.ResponseDto.success(tree, '获取成功');
    }
    async findOne(id) {
        const position = await this.positionsService.findOne(+id);
        return response_dto_1.ResponseDto.success(position, '获取成功');
    }
    async update(id, updatePositionDto, currentUser) {
        const position = await this.positionsService.update(+id, updatePositionDto, currentUser.id);
        return response_dto_1.ResponseDto.success(position, '更新成功');
    }
    async remove(id) {
        await this.positionsService.remove(+id);
        return response_dto_1.ResponseDto.success(null, '删除成功');
    }
    async batchRemove(body) {
        await this.positionsService.batchRemove(body.ids);
        return response_dto_1.ResponseDto.success(null, '删除成功');
    }
};
exports.PositionsController = PositionsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建岗位' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: position_entity_1.Position }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_position_dto_1.CreatePositionDto,
        user_entity_1.User]),
    __metadata("design:returntype", Promise)
], PositionsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取岗位列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [position_entity_1.Position] }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_position_dto_1.QueryPositionDto]),
    __metadata("design:returntype", Promise)
], PositionsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('tree'),
    (0, swagger_1.ApiOperation)({ summary: '获取岗位树形结构' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [position_entity_1.Position] }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_position_dto_1.QueryPositionDto]),
    __metadata("design:returntype", Promise)
], PositionsController.prototype, "findTree", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取岗位详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: position_entity_1.Position }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PositionsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新岗位' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: position_entity_1.Position }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_position_dto_1.UpdatePositionDto,
        user_entity_1.User]),
    __metadata("design:returntype", Promise)
], PositionsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除岗位' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PositionsController.prototype, "remove", null);
__decorate([
    (0, common_1.Delete)(),
    (0, swagger_1.ApiOperation)({ summary: '批量删除岗位' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PositionsController.prototype, "batchRemove", null);
exports.PositionsController = PositionsController = __decorate([
    (0, swagger_1.ApiTags)('岗位管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('positions'),
    __metadata("design:paramtypes", [positions_service_1.PositionsService])
], PositionsController);
//# sourceMappingURL=positions.controller.js.map