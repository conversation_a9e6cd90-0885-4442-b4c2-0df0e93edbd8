import { BaseEntity } from '@/common/entities/base.entity';
import { Organization } from '@/modules/organizations/entities/organization.entity';
export declare class Position extends BaseEntity {
    name: string;
    code: string;
    parentId: number;
    departmentId: number;
    type: string;
    sortOrder: number;
    status: number;
    description?: string;
    parent?: Position;
    children?: Position[];
    department?: Organization;
}
