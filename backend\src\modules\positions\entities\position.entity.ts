import { Entity, Column, Index, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '@/common/entities/base.entity';
import { Organization } from '@/modules/organizations/entities/organization.entity';

@Entity('positions')
@Index(['parentId'])
@Index(['departmentId'])
@Index(['code'])
@Index(['status'])
@Index(['sortOrder'])
export class Position extends BaseEntity {
  @ApiProperty({ description: '岗位名称' })
  @Column({
    type: 'varchar',
    length: 100,
    comment: '岗位名称',
  })
  name: string;

  @ApiProperty({ description: '岗位编码' })
  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    comment: '岗位编码',
  })
  code: string;

  @ApiProperty({ description: '主管上级岗位ID' })
  @Column({
    name: 'parent_id',
    type: 'bigint',
    default: 0,
    comment: '主管上级岗位ID',
  })
  parentId: number;

  @ApiProperty({ description: '归属部门ID' })
  @Column({
    name: 'department_id',
    type: 'bigint',
    comment: '归属部门ID',
  })
  departmentId: number;

  @ApiProperty({ description: '类别：department部门，office科室' })
  @Column({
    type: 'enum',
    enum: ['department', 'office'],
    default: 'department',
    comment: '类别：department部门，office科室',
  })
  type: string;

  @ApiProperty({ description: '排序' })
  @Column({
    name: 'sort_order',
    type: 'int',
    default: 0,
    comment: '排序',
  })
  sortOrder: number;

  @ApiProperty({ description: '状态：1启用，0停用' })
  @Column({
    type: 'tinyint',
    default: 1,
    comment: '状态：1启用，0停用',
  })
  status: number;

  @ApiProperty({ description: '岗位描述' })
  @Column({
    type: 'text',
    nullable: true,
    comment: '岗位描述',
  })
  description?: string;

  // 关联关系
  @ManyToOne(() => Position, (position) => position.children)
  @JoinColumn({ name: 'parent_id' })
  parent?: Position;

  @OneToMany(() => Position, (position) => position.parent)
  children?: Position[];

  @ManyToOne(() => Organization, { eager: true })
  @JoinColumn({ name: 'department_id' })
  department?: Organization;
}
