"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PositionsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const position_entity_1 = require("./entities/position.entity");
let PositionsService = class PositionsService {
    constructor(positionRepository) {
        this.positionRepository = positionRepository;
    }
    async create(createPositionDto, currentUserId) {
        const existingPosition = await this.positionRepository.findOne({
            where: { code: createPositionDto.code },
        });
        if (existingPosition) {
            throw new common_1.ConflictException('岗位编码已存在');
        }
        if (createPositionDto.parentId && createPositionDto.parentId !== 0) {
            const parentPosition = await this.positionRepository.findOne({
                where: { id: createPositionDto.parentId },
            });
            if (!parentPosition) {
                throw new common_1.NotFoundException('上级岗位不存在');
            }
        }
        const position = this.positionRepository.create({
            ...createPositionDto,
            parentId: createPositionDto.parentId || 0,
            sortOrder: createPositionDto.sortOrder || 0,
            status: createPositionDto.status ?? 1,
            createdBy: currentUserId,
        });
        return this.positionRepository.save(position);
    }
    async findAll(queryDto) {
        try {
            const queryBuilder = this.positionRepository
                .createQueryBuilder('position')
                .orderBy('position.sortOrder', 'ASC')
                .addOrderBy('position.createdAt', 'ASC');
            if (queryDto?.name) {
                queryBuilder.andWhere('position.name LIKE :name', { name: `%${queryDto.name}%` });
            }
            if (queryDto?.code) {
                queryBuilder.andWhere('position.code LIKE :code', { code: `%${queryDto.code}%` });
            }
            if (queryDto?.type) {
                queryBuilder.andWhere('position.type = :type', { type: queryDto.type });
            }
            if (queryDto?.status !== undefined) {
                queryBuilder.andWhere('position.status = :status', { status: queryDto.status });
            }
            if (queryDto?.parentId !== undefined) {
                queryBuilder.andWhere('position.parentId = :parentId', { parentId: queryDto.parentId });
            }
            if (queryDto?.departmentId !== undefined) {
                queryBuilder.andWhere('position.departmentId = :departmentId', { departmentId: queryDto.departmentId });
            }
            const positions = await queryBuilder.getMany();
            return positions.map(position => ({
                ...position,
                id: Number(position.id),
                parentId: Number(position.parentId),
                departmentId: Number(position.departmentId),
                departmentName: '',
            }));
        }
        catch (error) {
            console.error('查询岗位列表失败:', error);
            throw error;
        }
    }
    async findTree(queryDto) {
        const positions = await this.findAll(queryDto);
        return this.buildTree(positions);
    }
    async findOne(id) {
        const position = await this.positionRepository.findOne({
            where: { id },
            relations: ['department', 'parent', 'children'],
        });
        if (!position) {
            throw new common_1.NotFoundException('岗位不存在');
        }
        return position;
    }
    async update(id, updatePositionDto, currentUserId) {
        const position = await this.findOne(id);
        if (updatePositionDto.code && updatePositionDto.code !== position.code) {
            const existingPosition = await this.positionRepository.findOne({
                where: { code: updatePositionDto.code },
            });
            if (existingPosition && existingPosition.id !== id) {
                throw new common_1.ConflictException('岗位编码已存在');
            }
        }
        if (updatePositionDto.parentId && updatePositionDto.parentId !== 0) {
            if (updatePositionDto.parentId === id) {
                throw new common_1.ConflictException('不能将自己设置为上级岗位');
            }
            const parentPosition = await this.positionRepository.findOne({
                where: { id: updatePositionDto.parentId },
            });
            if (!parentPosition) {
                throw new common_1.NotFoundException('上级岗位不存在');
            }
            if (await this.isDescendant(updatePositionDto.parentId, id)) {
                throw new common_1.ConflictException('不能将子岗位设置为上级岗位');
            }
        }
        Object.assign(position, {
            ...updatePositionDto,
            updatedBy: currentUserId,
        });
        return this.positionRepository.save(position);
    }
    async remove(id) {
        const position = await this.findOne(id);
        const children = await this.positionRepository.find({
            where: { parentId: id },
        });
        if (children.length > 0) {
            throw new common_1.ConflictException('存在子岗位，无法删除');
        }
        await this.positionRepository.remove(position);
    }
    async batchRemove(ids) {
        for (const id of ids) {
            await this.remove(id);
        }
    }
    buildTree(positions, parentId = 0) {
        const tree = [];
        positions.forEach(position => {
            const positionParentId = Number(position.parentId);
            const currentParentId = Number(parentId);
            if (positionParentId === currentParentId) {
                const children = this.buildTree(positions, position.id);
                if (children.length > 0) {
                    position.children = children;
                }
                tree.push(position);
            }
        });
        return tree;
    }
    async isDescendant(ancestorId, descendantId) {
        const descendants = await this.getDescendants(descendantId);
        return descendants.some(desc => desc.id === ancestorId);
    }
    async getDescendants(parentId) {
        const descendants = [];
        const children = await this.positionRepository.find({
            where: { parentId },
        });
        for (const child of children) {
            descendants.push(child);
            const grandChildren = await this.getDescendants(child.id);
            descendants.push(...grandChildren);
        }
        return descendants;
    }
};
exports.PositionsService = PositionsService;
exports.PositionsService = PositionsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(position_entity_1.Position)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PositionsService);
//# sourceMappingURL=positions.service.js.map