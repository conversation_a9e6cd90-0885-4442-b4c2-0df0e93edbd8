import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUserDto } from './dto/query-user.dto';
import { PaginationResult } from '@/common/dto/pagination.dto';
export declare class UsersService {
    private userRepository;
    constructor(userRepository: Repository<User>);
    create(createUserDto: CreateUserDto, currentUserId?: number): Promise<User>;
    findAll(queryDto: QueryUserDto): Promise<PaginationResult<User>>;
    findOne(id: number): Promise<User>;
    findById(id: number): Promise<User | null>;
    findByUsername(username: string): Promise<User | null>;
    findByPhone(phone: string): Promise<User | null>;
    update(id: number, updateUserDto: UpdateUserDto, currentUserId?: number): Promise<User>;
    remove(id: number): Promise<void>;
    batchRemove(ids: number[]): Promise<void>;
    resetPassword(id: number, newPassword: string, currentUserId?: number): Promise<void>;
    updateStatus(id: number, status: number, currentUserId?: number): Promise<void>;
    updateLastLoginTime(id: number): Promise<void>;
    validatePassword(user: User, password: string): Promise<boolean>;
}
