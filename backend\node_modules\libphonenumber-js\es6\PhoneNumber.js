function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }

import Metadata, { validateMetadata } from './metadata.js';
import isPossibleNumber from './isPossible.js';
import isValidNumber from './isValid.js'; // import checkNumberLength from './helpers/checkNumberLength.js'

import getNumberType from './helpers/getNumberType.js';
import getPossibleCountriesForNumber from './helpers/getPossibleCountriesForNumber.js';
import extractCountryCallingCode from './helpers/extractCountryCallingCode.js';
import isObject from './helpers/isObject.js';
import formatNumber from './format.js';
var USE_NON_GEOGRAPHIC_COUNTRY_CODE = false;

var PhoneNumber = /*#__PURE__*/function () {
  /**
   * @param  {string} countryOrCountryCallingCode
   * @param  {string} nationalNumber
   * @param  {object} metadata — Metadata JSON
   * @return {PhoneNumber}
   */
  function PhoneNumber(countryOrCountryCallingCode, nationalNumber, metadata) {
    _classCallCheck(this, PhoneNumber);

    // Validate `countryOrCountryCallingCode` argument.
    if (!countryOrCountryCallingCode) {
      throw new TypeError('First argument is required');
    }

    if (typeof countryOrCountryCallingCode !== 'string') {
      throw new TypeError('First argument must be a string');
    } // In case of public API use: `constructor(number, metadata)`.
    // Transform the arguments from `constructor(number, metadata)` to
    // `constructor(countryOrCountryCallingCode, nationalNumber, metadata)`.


    if (countryOrCountryCallingCode[0] === '+' && !nationalNumber) {
      throw new TypeError('`metadata` argument not passed');
    }

    if (isObject(nationalNumber) && isObject(nationalNumber.countries)) {
      metadata = nationalNumber;
      var e164Number = countryOrCountryCallingCode;

      if (!E164_NUMBER_REGEXP.test(e164Number)) {
        throw new Error('Invalid `number` argument passed: must consist of a "+" followed by digits');
      }

      var _extractCountryCallin = extractCountryCallingCode(e164Number, undefined, undefined, metadata),
          _countryCallingCode = _extractCountryCallin.countryCallingCode,
          number = _extractCountryCallin.number;

      nationalNumber = number;
      countryOrCountryCallingCode = _countryCallingCode;

      if (!nationalNumber) {
        throw new Error('Invalid `number` argument passed: too short');
      }
    } // Validate `nationalNumber` argument.


    if (!nationalNumber) {
      throw new TypeError('`nationalNumber` argument is required');
    }

    if (typeof nationalNumber !== 'string') {
      throw new TypeError('`nationalNumber` argument must be a string');
    } // Validate `metadata` argument.


    validateMetadata(metadata); // Initialize properties.

    var _getCountryAndCountry = getCountryAndCountryCallingCode(countryOrCountryCallingCode, metadata),
        country = _getCountryAndCountry.country,
        countryCallingCode = _getCountryAndCountry.countryCallingCode;

    this.country = country;
    this.countryCallingCode = countryCallingCode;
    this.nationalNumber = nationalNumber;
    this.number = '+' + this.countryCallingCode + this.nationalNumber; // Exclude `metadata` property output from `PhoneNumber.toString()`
    // so that it doesn't clutter the console output of Node.js.
    // Previously, when Node.js did `console.log(new PhoneNumber(...))`,
    // it would output the whole internal structure of the `metadata` object.

    this.getMetadata = function () {
      return metadata;
    };
  }

  _createClass(PhoneNumber, [{
    key: "setExt",
    value: function setExt(ext) {
      this.ext = ext;
    }
  }, {
    key: "getPossibleCountries",
    value: function getPossibleCountries() {
      if (this.country) {
        return [this.country];
      }

      return getPossibleCountriesForNumber(this.countryCallingCode, this.nationalNumber, this.getMetadata());
    }
  }, {
    key: "isPossible",
    value: function isPossible() {
      return isPossibleNumber(this, {
        v2: true
      }, this.getMetadata());
    }
  }, {
    key: "isValid",
    value: function isValid() {
      return isValidNumber(this, {
        v2: true
      }, this.getMetadata());
    }
  }, {
    key: "isNonGeographic",
    value: function isNonGeographic() {
      var metadata = new Metadata(this.getMetadata());
      return metadata.isNonGeographicCallingCode(this.countryCallingCode);
    }
  }, {
    key: "isEqual",
    value: function isEqual(phoneNumber) {
      return this.number === phoneNumber.number && this.ext === phoneNumber.ext;
    } // This function was originally meant to be an equivalent for `validatePhoneNumberLength()`,
    // but later it was found out that it doesn't include the possible `TOO_SHORT` result
    // returned from `parsePhoneNumberWithError()` in the original `validatePhoneNumberLength()`,
    // so eventually I simply commented out this method from the `PhoneNumber` class
    // and just left the `validatePhoneNumberLength()` function, even though that one would require
    // and additional step to also validate the actual country / calling code of the phone number.
    // validateLength() {
    // 	const metadata = new Metadata(this.getMetadata())
    // 	metadata.selectNumberingPlan(this.countryCallingCode)
    // 	const result = checkNumberLength(this.nationalNumber, metadata)
    // 	if (result !== 'IS_POSSIBLE') {
    // 		return result
    // 	}
    // }

  }, {
    key: "getType",
    value: function getType() {
      return getNumberType(this, {
        v2: true
      }, this.getMetadata());
    }
  }, {
    key: "format",
    value: function format(_format, options) {
      return formatNumber(this, _format, options ? _objectSpread(_objectSpread({}, options), {}, {
        v2: true
      }) : {
        v2: true
      }, this.getMetadata());
    }
  }, {
    key: "formatNational",
    value: function formatNational(options) {
      return this.format('NATIONAL', options);
    }
  }, {
    key: "formatInternational",
    value: function formatInternational(options) {
      return this.format('INTERNATIONAL', options);
    }
  }, {
    key: "getURI",
    value: function getURI(options) {
      return this.format('RFC3966', options);
    }
  }]);

  return PhoneNumber;
}();

export { PhoneNumber as default };

var isCountryCode = function isCountryCode(value) {
  return /^[A-Z]{2}$/.test(value);
};

function getCountryAndCountryCallingCode(countryOrCountryCallingCode, metadataJson) {
  var country;
  var countryCallingCode;
  var metadata = new Metadata(metadataJson); // If country code is passed then derive `countryCallingCode` from it.
  // Also store the country code as `.country`.

  if (isCountryCode(countryOrCountryCallingCode)) {
    country = countryOrCountryCallingCode;
    metadata.selectNumberingPlan(country);
    countryCallingCode = metadata.countryCallingCode();
  } else {
    countryCallingCode = countryOrCountryCallingCode;
    /* istanbul ignore if */

    if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {
      if (metadata.isNonGeographicCallingCode(countryCallingCode)) {
        country = '001';
      }
    }
  }

  return {
    country: country,
    countryCallingCode: countryCallingCode
  };
}

var E164_NUMBER_REGEXP = /^\+\d+$/;
//# sourceMappingURL=PhoneNumber.js.map