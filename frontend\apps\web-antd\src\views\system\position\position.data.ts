// 岗位类别选项
export const positionTypeOptions = [
  { label: '部门', value: 'department' },
  { label: '科室', value: 'office' },
];

// 状态选项
export const statusOptions = [
  { label: '全部', value: null },
  { label: '启用', value: 1 },
  { label: '停用', value: 0 },
];

// 表格列配置
export const columns = [
  {
    title: '岗位名称',
    dataIndex: 'name',
    field: 'name',
    width: 200,
    treeNode: true,
  },
  {
    title: '岗位编码',
    dataIndex: 'code',
    field: 'code',
    width: 150,
  },
  {
    title: '类别',
    dataIndex: 'type',
    field: 'type',
    width: 100,
    slots: { default: 'type' },
  },
  {
    title: '排序',
    dataIndex: 'sortOrder',
    field: 'sortOrder',
    width: 80,
  },
  {
    title: '归属部门',
    dataIndex: 'departmentName',
    field: 'departmentName',
    width: 150,
  },
  {
    title: '状态',
    dataIndex: 'status',
    field: 'status',
    width: 80,
    slots: { default: 'status' },
  },
  {
    title: '描述',
    dataIndex: 'description',
    field: 'description',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    field: 'createdAt',
    width: 180,
  },
  {
    field: 'action',
    fixed: 'right' as const,
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

// 搜索表单配置
export const searchFormSchema = [
  {
    label: '岗位名称',
    fieldName: 'name',
    component: 'Input',
    componentProps: {
      placeholder: '请输入岗位名称',
    },
  },
  {
    label: '岗位编码',
    fieldName: 'code',
    component: 'Input',
    componentProps: {
      placeholder: '请输入岗位编码',
    },
  },
  {
    label: '类别',
    fieldName: 'type',
    component: 'Select',
    componentProps: {
      options: positionTypeOptions,
      placeholder: '请选择类别',
    },
  },
  {
    label: '状态',
    fieldName: 'status',
    component: 'Select',
    componentProps: {
      options: statusOptions,
      placeholder: '请选择状态',
    },
  },
];

// 表单配置
export const formSchema = [
  {
    fieldName: 'parentId',
    label: '上级岗位',
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择上级岗位',
      treeDefaultExpandAll: true,
      showSearch: true,
      treeNodeFilterProp: 'title',
      allowClear: true,
    },
  },
  {
    fieldName: 'name',
    label: '岗位名称',
    rules: 'required',
    component: 'Input',
    componentProps: {
      placeholder: '请输入岗位名称',
    },
  },
  {
    fieldName: 'code',
    label: '岗位编码',
    rules: 'required',
    component: 'Input',
    componentProps: {
      placeholder: '请输入岗位编码',
    },
  },
  {
    fieldName: 'departmentId',
    label: '归属部门',
    rules: 'required',
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择归属部门',
      treeDefaultExpandAll: true,
      showSearch: true,
      treeNodeFilterProp: 'title',
    },
  },
  {
    fieldName: 'type',
    label: '岗位类别',
    rules: 'required',
    component: 'Select',
    componentProps: {
      options: positionTypeOptions,
      placeholder: '请选择岗位类别',
    },
  },
  {
    fieldName: 'sortOrder',
    label: '排序',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入排序',
      min: 0,
    },
  },
  {
    fieldName: 'status',
    label: '状态',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '停用', value: 0 },
      ],
    },
    defaultValue: 1,
  },
  {
    fieldName: 'description',
    label: '岗位描述',
    component: 'Textarea',
    componentProps: {
      placeholder: '请输入岗位描述',
      rows: 3,
    },
  },
];

// 获取类别标签
export const getTypeLabel = (type: string) => {
  const option = positionTypeOptions.find(item => item.value === type);
  return option ? option.label : type;
};

// 获取状态标签
export const getStatusLabel = (status: number) => {
  return status === 1 ? '启用' : '停用';
};

// 构建树形选择数据
export const buildTreeSelectData = (data: any[], valueField = 'id', titleField = 'name', childrenField = 'children') => {
  return data.map(item => ({
    value: item[valueField],
    title: item[titleField],
    key: item[valueField],
    children: item[childrenField] ? buildTreeSelectData(item[childrenField], valueField, titleField, childrenField) : undefined,
  }));
};

// 构建树形结构
export const buildTree = (data: any[], parentId = 0) => {
  const tree: any[] = [];

  data.forEach(item => {
    if (Number(item.parentId) === Number(parentId)) {
      const children = buildTree(data, item.id);
      if (children.length > 0) {
        item.children = children;
      }
      tree.push(item);
    }
  });

  return tree;
};
