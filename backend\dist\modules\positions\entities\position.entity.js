"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Position = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_entity_1 = require("../../../common/entities/base.entity");
const organization_entity_1 = require("../../organizations/entities/organization.entity");
let Position = class Position extends base_entity_1.BaseEntity {
};
exports.Position = Position;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '岗位名称' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        comment: '岗位名称',
    }),
    __metadata("design:type", String)
], Position.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '岗位编码' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        unique: true,
        comment: '岗位编码',
    }),
    __metadata("design:type", String)
], Position.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '主管上级岗位ID' }),
    (0, typeorm_1.Column)({
        name: 'parent_id',
        type: 'bigint',
        default: 0,
        comment: '主管上级岗位ID',
    }),
    __metadata("design:type", Number)
], Position.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '归属部门ID' }),
    (0, typeorm_1.Column)({
        name: 'department_id',
        type: 'bigint',
        comment: '归属部门ID',
    }),
    __metadata("design:type", Number)
], Position.prototype, "departmentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '类别：department部门，office科室' }),
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['department', 'office'],
        default: 'department',
        comment: '类别：department部门，office科室',
    }),
    __metadata("design:type", String)
], Position.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序' }),
    (0, typeorm_1.Column)({
        name: 'sort_order',
        type: 'int',
        default: 0,
        comment: '排序',
    }),
    __metadata("design:type", Number)
], Position.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1启用，0停用' }),
    (0, typeorm_1.Column)({
        type: 'tinyint',
        default: 1,
        comment: '状态：1启用，0停用',
    }),
    __metadata("design:type", Number)
], Position.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '岗位描述' }),
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: '岗位描述',
    }),
    __metadata("design:type", String)
], Position.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Position, (position) => position.children),
    (0, typeorm_1.JoinColumn)({ name: 'parent_id' }),
    __metadata("design:type", Position)
], Position.prototype, "parent", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Position, (position) => position.parent),
    __metadata("design:type", Array)
], Position.prototype, "children", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => organization_entity_1.Organization, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'department_id' }),
    __metadata("design:type", organization_entity_1.Organization)
], Position.prototype, "department", void 0);
exports.Position = Position = __decorate([
    (0, typeorm_1.Entity)('positions'),
    (0, typeorm_1.Index)(['parentId']),
    (0, typeorm_1.Index)(['departmentId']),
    (0, typeorm_1.Index)(['code']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['sortOrder'])
], Position);
//# sourceMappingURL=position.entity.js.map