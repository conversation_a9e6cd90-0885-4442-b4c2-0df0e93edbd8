// 组织类别选项
export const organizationTypeOptions = [
  { label: '部门', value: 'department' },
  { label: '科室', value: 'office' },
];

// 状态选项
export const statusOptions = [
  { label: '全部', value: null },
  { label: '启用', value: 1 },
  { label: '停用', value: 0 },
];

// 表格列配置
export const columns = [
  {
    title: '机构名称',
    dataIndex: 'name',
    field: 'name',
    width: 200,
    treeNode: true,
  },
  {
    title: '机构编码',
    dataIndex: 'code',
    field: 'code',
    width: 150,
  },
  {
    title: '类别',
    dataIndex: 'type',
    field: 'type',
    width: 100,
    slots: { default: 'type' },
  },
  {
    title: '排序',
    dataIndex: 'sortOrder',
    field: 'sortOrder',
    width: 80,
  },
  {
    title: '默认角色',
    dataIndex: 'defaultRoleName',
    field: 'defaultRoleName',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    field: 'status',
    width: 80,
    slots: { default: 'status' },
  },
  {
    title: '描述',
    dataIndex: 'description',
    field: 'description',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    field: 'createdAt',
    width: 180,
  },
  {
    field: 'action',
    fixed: 'right' as const,
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

// 搜索表单配置
export const searchFormSchema = [
  {
    label: '机构名称',
    fieldName: 'name',
    component: 'Input',
    componentProps: {
      placeholder: '请输入机构名称',
    },
  },
  {
    label: '机构编码',
    fieldName: 'code',
    component: 'Input',
    componentProps: {
      placeholder: '请输入机构编码',
    },
  },
  {
    label: '类别',
    fieldName: 'type',
    component: 'Select',
    componentProps: {
      options: organizationTypeOptions,
      placeholder: '请选择类别',
    },
  },
  {
    label: '状态',
    fieldName: 'status',
    component: 'Select',
    componentProps: {
      options: statusOptions,
      placeholder: '请选择状态',
    },
  },
];

// 表单配置
export const formSchema = [
  // {
  //   fieldName: 'id',
  //   label: 'ID',
  //   component: 'Input',
  //   dependencies: {
  //     show: false,
  //   },
  // },
  {
    fieldName: 'parentId',
    label: '上级机构',
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择上级机构',
      treeDefaultExpandAll: true,
      showSearch: true,
      treeNodeFilterProp: 'title',
      allowClear: true,
    },
  },
  {
    fieldName: 'name',
    label: '机构名称',
    rules: 'required',
    component: 'Input',
    componentProps: {
      placeholder: '请输入机构名称',
    },
  },
  {
    fieldName: 'code',
    label: '机构编码',
    rules: 'required',
    component: 'Input',
    componentProps: {
      placeholder: '请输入机构编码',
    },
  },

  {
    fieldName: 'type',
    label: '类别',
    rules: 'selectRequired',
    component: 'Select',
    componentProps: {
      options: organizationTypeOptions,
      placeholder: '请选择类别',
    },
  },
   {
    fieldName: 'defaultRoleId',
    label: '默认角色',
    component: 'Select',
    componentProps: {
      placeholder: '请选择默认角色',
      allowClear: true,
    },
  },
  {
    fieldName: 'sortOrder',
    label: '排序',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入排序号',
      min: 0,
    },
  },
  {
    fieldName: 'status',
    label: '状态',
    component: 'RadioGroup',
    componentProps: {
      options: statusOptions,
    },
    defaultValue: 1,
  },
  {
    fieldName: 'description',
    label: '描述',
    component: 'Textarea',
    componentProps: {
      placeholder: '请输入描述',
      rows: 3,
    },
  },
];

// 获取类别标签
export function getTypeLabel(type: string): string {
  const option = organizationTypeOptions.find(item => item.value === type);
  return option ? option.label : type;
}

// 获取状态标签
export function getStatusLabel(status: number): string {
  const option = statusOptions.find(item => item.value === status);
  return option ? option.label : String(status);
}

// 构建树形数据
export function buildTree(list: any[], parentId = 0): any[] {
  const tree: any[] = [];

  list.forEach(item => {
    if (item.parentId === parentId || parentId === 0) {
      const children = buildTree(list, item.id);
      if (children.length > 0) {
        item.children = children;
      }
      tree.push(item);
    }
  });

  return tree;
}

// 构建树形选择器数据
export function buildTreeSelectData(list: any[], parentId = 0): any[] {
  const tree: any[] = [];

  list.forEach(item => {
      const node: any = {
        title: item.name,
        value: item.id,
        key: item.id,
      };

      if(item.children && item.children.length > 0){
        const children = buildTreeSelectData(item.children, item.id);
        if (children.length > 0) {
          node.children = children;
        }
      }

      tree.push(node);
  });

  return tree;
}
