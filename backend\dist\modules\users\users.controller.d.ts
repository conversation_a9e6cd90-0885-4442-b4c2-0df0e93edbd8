import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUserDto } from './dto/query-user.dto';
import { User } from './entities/user.entity';
import { ResponseDto } from '@/common/dto/response.dto';
import { PaginationResult } from '@/common/dto/pagination.dto';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    create(createUserDto: CreateUserDto, currentUser: User): Promise<ResponseDto<User>>;
    findAll(queryDto: QueryUserDto): Promise<ResponseDto<PaginationResult<User>>>;
    findOne(id: string): Promise<ResponseDto<User>>;
    update(id: string, updateUserDto: UpdateUserDto, currentUser: User): Promise<ResponseDto<User>>;
    remove(id: string): Promise<ResponseDto<void>>;
    batchRemove(ids: number[]): Promise<ResponseDto<void>>;
    resetPassword(id: string, password: string, currentUser: User): Promise<ResponseDto<void>>;
    updateStatus(id: string, status: number, currentUser: User): Promise<ResponseDto<void>>;
}
