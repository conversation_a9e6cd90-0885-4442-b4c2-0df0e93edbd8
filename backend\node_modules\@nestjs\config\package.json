{"name": "@nestjs/config", "version": "4.0.2", "description": "Nest - modern, fast, powerful node.js web framework (@config)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "url": "https://github.com/nestjs/config#readme", "scripts": {"build": "rimraf -rf dist && tsc -p tsconfig.json", "format": "prettier --write \"{lib,test}/**/*.ts\"", "lint": "eslint 'lib/**/*.ts' --fix", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "test:integration": "jest --config ./tests/jest-e2e.json --runInBand", "prerelease": "npm run build", "release": "release-it"}, "dependencies": {"dotenv": "16.4.7", "dotenv-expand": "12.0.1", "lodash": "4.17.21"}, "devDependencies": {"@commitlint/cli": "19.8.0", "@commitlint/config-angular": "19.8.0", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.23.0", "@nestjs/common": "11.0.12", "@nestjs/core": "11.0.12", "@nestjs/platform-express": "11.0.12", "@nestjs/testing": "11.0.12", "@types/jest": "29.5.14", "@types/lodash": "4.17.16", "@types/node": "22.13.13", "eslint": "9.23.0", "eslint-config-prettier": "10.1.1", "eslint-plugin-prettier": "5.2.5", "globals": "16.0.0", "husky": "9.1.7", "jest": "29.7.0", "joi": "17.13.3", "lint-staged": "15.5.0", "prettier": "3.5.3", "reflect-metadata": "0.2.2", "release-it": "18.1.2", "rimraf": "6.0.1", "rxjs": "7.8.2", "ts-jest": "29.3.0", "typescript": "5.8.2", "typescript-eslint": "8.28.0"}, "peerDependencies": {"@nestjs/common": "^10.0.0 || ^11.0.0", "rxjs": "^7.1.0"}, "lint-staged": {"*.ts": ["prettier --write"]}, "husky": {"hooks": {"commit-msg": "commitlint -c .commitlintrc.json -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "repository": {"type": "git", "url": "https://github.com/nestjs/config"}}