import { ObservableInputTuple, OperatorFunction } from '../types';
export declare function onErrorResumeNextWith<T, A extends readonly unknown[]>(sources: [...ObservableInputTuple<A>]): OperatorFunction<T, T | A[number]>;
export declare function onErrorResumeNextWith<T, A extends readonly unknown[]>(...sources: [...ObservableInputTuple<A>]): OperatorFunction<T, T | A[number]>;
/**
 * @deprecated Renamed. Use {@link onErrorResumeNextWith} instead. Will be removed in v8.
 */
export declare const onErrorResumeNext: typeof onErrorResumeNextWith;
//# sourceMappingURL=onErrorResumeNextWith.d.ts.map